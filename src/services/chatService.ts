import { ApiResponse, apiService } from './api';
import { 
  Chat, 
  ChatListResponse, 
  Message, 
  MessagesResponse, 
  SendMessageRequest, 
  SendMessageResponse 
} from '@/types/chat';

/**
 * Chat Service
 * Handles all chat-related API operations using the existing apiService
 */
export const chatService = {
  /**
   * Get list of chats for the current user
   * @param token - Authorization token
   * @returns Promise with chat list response
   */
  getChats: async (token?: string): Promise<ApiResponse<ChatListResponse>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await apiService<any>('/api/chats', {
      method: 'GET',
      headers,
      requiresAuth: true,
    });

    if (response.isSuccess && Array.isArray(response.data)) {
      // Transform the raw API response into the expected ChatListResponse format
      const transformedChats: Chat[] = response.data.map(chat => ({
        id: chat.id,
        type: chat.type,
        created_at: chat.created_at,
        updated_at: chat.updated_at,
        participants: chat.participants.map((id: number) => ({
          id: id.toString(),
          name: 'Loading...', // This will be updated when we implement user details fetching
          type: 'customer' // Default type, should be updated based on user role
        })),
        last_message: undefined,
        unread_count: 0
      }));

      return {
        ...response,
        data: {
          data: transformedChats,
          meta: {
            current_page: 1,
            last_page: 1,
            per_page: transformedChats.length,
            total: transformedChats.length
          }
        }
      };
    }

    return response;
  },

  /**
   * Get messages for a specific chat
   * @param chatId - Chat ID
   * @param page - Page number for pagination (optional)
   * @param token - Authorization token
   * @returns Promise with messages response
   */
  getMessages: async (
    chatId: string, 
    page: number = 1, 
    token?: string
  ): Promise<ApiResponse<MessagesResponse>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    let endpoint = `/api/chats/${chatId}/messages`;
    if (page > 1) {
      endpoint += `?page=${page}`;
    }

    return apiService<MessagesResponse>(endpoint, {
      method: 'GET',
      headers,
      requiresAuth: true,
    });
  },

  /**
   * Send a message to a chat
   * @param chatId - Chat ID
   * @param messageData - Message content and type
   * @param token - Authorization token
   * @returns Promise with sent message response
   */
  sendMessage: async (
    chatId: string, 
    messageData: SendMessageRequest, 
    token?: string
  ): Promise<ApiResponse<SendMessageResponse>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return apiService<SendMessageResponse>(`/api/chats/${chatId}/messages`, {
      method: 'POST',
      headers,
      body: messageData,
      requiresAuth: true,
    });
  },

  /**
   * Mark all messages in a chat as read
   * @param chatId - Chat ID
   * @param token - Authorization token
   * @returns Promise with success response
   */
  markAsRead: async (
    chatId: string, 
    token?: string
  ): Promise<ApiResponse<{ success: boolean; message?: string }>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return apiService<{ success: boolean; message?: string }>(`/api/chats/${chatId}/read`, {
      method: 'POST',
      headers,
      requiresAuth: true,
    });
  },

  /**
   * Delete a specific message
   * @param chatId - Chat ID
   * @param messageId - Message ID
   * @param token - Authorization token
   * @returns Promise with success response
   */
  deleteMessage: async (
    chatId: string, 
    messageId: string, 
    token?: string
  ): Promise<ApiResponse<{ success: boolean; message?: string }>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return apiService<{ success: boolean; message?: string }>(
      `/api/chats/${chatId}/messages/${messageId}`, 
      {
        method: 'DELETE',
        headers,
        requiresAuth: true,
      }
    );
  },

  /**
   * Create a new chat (if needed for admin functionality)
   * @param userId - User ID to include in chat
   * @param type - Chat type ('direct' or 'group')
   * @param name - Chat name (optional, for group chats)
   * @param token - Authorization token
   * @returns Promise with created chat response
   */
  createChat: async (
    userId: string,
    type: 'direct' | 'group' = 'direct',
    name?: string,
    token?: string
  ): Promise<ApiResponse<{ data: Chat; success: boolean; message?: string }>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return apiService<{ data: Chat; success: boolean; message?: string }>('/api/chats', {
      method: 'POST',
      headers,
      body: { user_id: userId, type, name },
      requiresAuth: true,
    });
  },
};
