
import { useState, useEffect, useRef } from "react";
import { ConversationList } from "./ConversationList";
import { MessageThread } from "./MessageThread";
import { MessageComposer } from "./MessageComposer";
import { ConversationHeader } from "./ConversationHeader";
import { MobileBackButton } from "./MobileBackButton";
import { useUIHelpers } from "@/hooks/use-ui-helpers";
import { Card } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CustomerProviderConversations } from "./CustomerProviderConversations";
import { MobileConversationCard } from "./MobileConversationCard";
import { MobileFilterBar } from "./MobileFilterBar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { MessageSquare, ClipboardList, AlertCircle, ArrowLeft, Wifi, WifiOff, Search } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useChat } from "@/hooks/useChat";
import { Chat } from "@/types/chat";

export const AdminMessagingInterface = () => {
  const { isMobile } = useUIHelpers();
  const { toast } = useToast();
  const {
    chats,
    currentChat,
    isLoading,
    isConnected,
    error,
    loadChats,
    joinChat,
    leaveChat,
    reconnect
  } = useChat();

  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);
  const [showMessageThread, setShowMessageThread] = useState(!isMobile);
  const [filter, setFilter] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const messageEndRef = useRef<HTMLDivElement>(null);
  const [activeTab, setActiveTab] = useState("direct");

  // Load chats on component mount
  useEffect(() => {
    loadChats().catch((error) => {
      console.error("Error loading chats:", error);
      toast({
        title: "Error loading chats",
        description: "There was an error loading your chats. Please try again.",
        variant: "destructive"
      });
    });
  }, [loadChats, toast]);

  // Show error toast when there's an error
  useEffect(() => {
    if (error) {
      toast({
        title: "Chat Error",
        description: error,
        variant: "destructive"
      });
    }
  }, [error, toast]);

  // Calculate counts for filter badges
  const unreadCount = (chats || []).reduce((total, chat) => total + (chat.unread_count || 0), 0);
  const flaggedCount = 0; // TODO: Implement flagged functionality when backend supports it

  // Get filtered chats based on search and filter
  const getFilteredChats = () => {
    return (chats || []).filter(chat => {
      // Get the other participant (not admin)
      const otherParticipant = chat.participants.find(p => p.type !== 'admin');
      const participantName = otherParticipant?.name || 'Unknown';

      const matchesSearch = searchQuery
        ? participantName.toLowerCase().includes(searchQuery.toLowerCase())
        : true;

      const matchesFilter =
        filter === "all" ||
        (filter === "unread" && (chat.unread_count || 0) > 0) ||
        (filter === "providers" && otherParticipant?.type === 'provider') ||
        (filter === "customers" && otherParticipant?.type === 'customer');
        // TODO: Add flagged and pinned filters when backend supports them

      return matchesSearch && matchesFilter;
    });
  };

  // Scroll to bottom when new messages are added
  useEffect(() => {
    if (messageEndRef.current && selectedChat) {
      messageEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [selectedChat]);

  const handleSelectChat = (chat: Chat) => {
    try {
      setSelectedChat(chat);
      if (isMobile) {
        setShowMessageThread(true);
      }

      // Join the chat for real-time updates
      joinChat(chat.id);
    } catch (error) {
      console.error("Error selecting chat:", error);
      toast({
        title: "Error",
        description: "There was an error selecting this chat. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleBackToList = () => {
    setShowMessageThread(false);
    leaveChat(); // Leave current chat when going back to list
  };

  // Helper function to get the other participant in a chat (not admin)
  const getOtherParticipant = (chat: Chat) => {
    return chat.participants.find(p => p.type !== 'admin') || chat.participants[0];
  };

  // Helper function to get status badge based on participant type
  const getStatusBadge = (participantType: string) => {
    return participantType === 'customer'
      ? 'bg-purple-100 text-purple-800 border-purple-200'
      : 'bg-green-100 text-green-800 border-green-200';
  };

  const filteredChats = getFilteredChats();

  // Handle empty state or error state
  const renderEmptyOrErrorState = () => {
    if (error) {
      return (
        <div className="flex flex-col items-center justify-center h-[200px] p-6 text-center">
          <div className="h-12 w-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mb-4">
            <AlertCircle className="h-6 w-6 text-red-500" />
          </div>
          <h3 className="text-lg font-medium mb-2">Something went wrong</h3>
          <p className="text-muted-foreground text-sm mb-4">
            {error}
          </p>
          <Button onClick={() => loadChats()} variant="outline" size="sm">
            Try Again
          </Button>
        </div>
      );
    }

    if (isLoading) {
      return (
        <div className="flex flex-col items-center justify-center h-[200px]">
          <div className="h-8 w-8 border-4 border-t-primary border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin mb-4"></div>
          <p className="text-muted-foreground">Loading chats...</p>
        </div>
      );
    }

    if (filteredChats.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center h-[200px] p-6 text-center">
          <div className="h-12 w-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
            <MessageSquare className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium mb-2">No chats found</h3>
          <p className="text-muted-foreground text-sm">
            {searchQuery
              ? "Try adjusting your search or filters"
              : "Start communicating with customers and providers"}
          </p>
        </div>
      );
    }

    return null;
  };

  return (
    <div className="flex flex-col h-[calc(100vh-64px)]">
      <div className="p-4 flex-grow overflow-hidden">
        <div className="mb-2 flex items-center justify-between">
          <div className="flex items-center">
            <MessageSquare className="h-5 w-5 mr-2 text-indigo-500" />
            <h2 className="text-2xl font-bold tracking-tight">Admin Messages</h2>
          </div>
          <div className="flex items-center gap-2">
            {/* Connection status indicator */}
            <div className="flex items-center gap-1">
              {isConnected ? (
                <Wifi className="h-4 w-4 text-green-500" />
              ) : (
                <WifiOff className="h-4 w-4 text-red-500" />
              )}
              <span className={`text-xs ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
            {!isConnected && (
              <Button onClick={reconnect} variant="outline" size="sm">
                Reconnect
              </Button>
            )}
          </div>
        </div>
        <p className="text-muted-foreground mb-4 animate-fade-in">Manage all communications across the platform</p>
        
        <Tabs
          defaultValue="direct"
          value={activeTab}
          onValueChange={(value) => {
            setActiveTab(value);
            setShowMessageThread(!isMobile); // Reset mobile view when switching tabs
          }}
          className="mb-4"
        >
          <TabsList className="w-full md:w-auto bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-950/40 dark:to-blue-950/40 border border-indigo-100 dark:border-indigo-900 p-1 shadow-sm rounded-lg">
            <TabsTrigger 
              value="direct" 
              className="text-sm md:text-base py-2.5 px-4 data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-500 data-[state=active]:to-blue-600 data-[state=active]:text-white data-[state=active]:shadow rounded-md"
            >
              <MessageSquare className="h-4 w-4 mr-1.5" />
              <span>Direct Messages</span>
            </TabsTrigger>
            <TabsTrigger 
              value="oversight" 
              className="text-sm md:text-base py-2.5 px-4 data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-500 data-[state=active]:to-blue-600 data-[state=active]:text-white data-[state=active]:shadow rounded-md"
            >
              <ClipboardList className="h-4 w-4 mr-1.5" />
              <span>Conversation Oversight</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="direct" className="mt-6 animate-fade-in">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 h-[calc(100vh-200px)]">
              {/* Conversation List - Show on desktop or on mobile when not viewing a thread */}
              {(!isMobile || !showMessageThread) && (
                <Card className="p-4 flex flex-col h-full md:col-span-1 overflow-hidden bg-gradient-to-b from-indigo-50 to-white dark:from-indigo-950/40 dark:to-gray-800 border-0 shadow-md rounded-xl">
                  {isMobile ? (
                    <>
                      <MobileFilterBar
                        searchQuery={searchQuery}
                        onSearchQueryChange={setSearchQuery}
                        filter={filter}
                        onFilterChange={setFilter}
                        totalConversations={(chats || []).length}
                        unreadCount={unreadCount}
                        flaggedCount={flaggedCount}
                      />
                      
                      <div className="mt-3 mb-2 flex flex-wrap gap-2">
                        <Badge 
                          onClick={() => setFilter("all")} 
                          className={`cursor-pointer py-1.5 px-3 rounded-full text-xs font-medium ${
                            filter === "all" 
                              ? "bg-gradient-to-r from-indigo-500 to-blue-600 text-white border-0" 
                              : "bg-white text-gray-700 border border-gray-200 hover:bg-gray-50"
                          }`}
                        >
                          All
                        </Badge>
                        
                        <Badge 
                          onClick={() => setFilter("unread")} 
                          className={`cursor-pointer py-1.5 px-3 rounded-full text-xs font-medium ${
                            filter === "unread" 
                              ? "bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-0" 
                              : "bg-white text-gray-700 border border-gray-200 hover:bg-gray-50"
                          }`}
                        >
                          Unread {unreadCount > 0 && `(${unreadCount})`}
                        </Badge>
                        
                        <Badge 
                          onClick={() => setFilter("flagged")} 
                          className={`cursor-pointer py-1.5 px-3 rounded-full text-xs font-medium ${
                            filter === "flagged" 
                              ? "bg-gradient-to-r from-rose-500 to-pink-600 text-white border-0" 
                              : "bg-white text-gray-700 border border-gray-200 hover:bg-gray-50"
                          }`}
                        >
                          Flagged {flaggedCount > 0 && `(${flaggedCount})`}
                        </Badge>
                        
                        <Badge 
                          onClick={() => setFilter("pinned")} 
                          className={`cursor-pointer py-1.5 px-3 rounded-full text-xs font-medium ${
                            filter === "pinned" 
                              ? "bg-gradient-to-r from-orange-500 to-amber-500 text-white border-0" 
                              : "bg-white text-gray-700 border border-gray-200 hover:bg-gray-50"
                          }`}
                        >
                          Pinned
                        </Badge>
                      </div>
                      
                      <ScrollArea className="flex-grow pr-2 mt-3">
                        {renderEmptyOrErrorState() || (
                          filteredChats.map((chat) => {
                            const otherParticipant = getOtherParticipant(chat);
                            // Convert Chat to AdminConversation format for compatibility
                            const conversationData = {
                              id: chat.id,
                              recipientId: otherParticipant.id,
                              recipientType: otherParticipant.type as 'customer' | 'provider',
                              recipientName: otherParticipant.name,
                              recipientAvatar: otherParticipant.avatar,
                              lastMessage: chat.last_message?.message || 'No messages yet',
                              timestamp: chat.last_message?.created_at || chat.created_at,
                              unread: (chat.unread_count || 0) > 0,
                              flagged: false, // TODO: Implement when backend supports it
                              pinned: false   // TODO: Implement when backend supports it
                            };

                            return (
                              <MobileConversationCard
                                key={chat.id}
                                conversation={conversationData}
                                isActive={selectedChat?.id === chat.id}
                                onClick={() => handleSelectChat(chat)}
                                getStatusBadge={getStatusBadge}
                              />
                            );
                          })
                        )}
                      </ScrollArea>
                    </>
                  ) : (
                    <div className="flex flex-col h-full">
                      {/* TODO: Create a new ChatList component that works with the new Chat type */}
                      {/* For now, we'll render a simple list */}
                      <div className="mb-4 space-y-2">
                        <div className="relative">
                          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                          <Input
                            placeholder="Search chats..."
                            className="pl-8"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                          />
                        </div>
                        <div className="flex space-x-1 overflow-x-auto">
                          <Button
                            variant={filter === "all" ? "default" : "outline"}
                            size="sm"
                            onClick={() => setFilter("all")}
                          >
                            All
                          </Button>
                          <Button
                            variant={filter === "providers" ? "default" : "outline"}
                            size="sm"
                            onClick={() => setFilter("providers")}
                          >
                            Providers
                          </Button>
                          <Button
                            variant={filter === "customers" ? "default" : "outline"}
                            size="sm"
                            onClick={() => setFilter("customers")}
                          >
                            Customers
                          </Button>
                          <Button
                            variant={filter === "unread" ? "default" : "outline"}
                            size="sm"
                            onClick={() => setFilter("unread")}
                          >
                            Unread
                          </Button>
                        </div>
                      </div>

                      <ScrollArea className="flex-grow">
                        {renderEmptyOrErrorState() || (
                          <div className="space-y-1">
                            {filteredChats.map((chat) => {
                              const otherParticipant = getOtherParticipant(chat);
                              return (
                                <div
                                  key={chat.id}
                                  onClick={() => handleSelectChat(chat)}
                                  className={`flex items-start p-3 rounded-md cursor-pointer hover:bg-muted ${
                                    selectedChat?.id === chat.id
                                      ? "bg-primary/5 border border-primary/10"
                                      : "border border-transparent"
                                  }`}
                                >
                                  <Avatar className="h-10 w-10 mr-3">
                                    <AvatarImage src={otherParticipant.avatar} />
                                    <AvatarFallback>
                                      {otherParticipant.name
                                        .split(" ")
                                        .map((n) => n[0])
                                        .join("")}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div className="flex-grow min-w-0">
                                    <div className="flex justify-between items-start">
                                      <h4 className="font-medium truncate">
                                        {otherParticipant.name}
                                      </h4>
                                      <span className="text-xs text-muted-foreground whitespace-nowrap">
                                        {new Date(chat.last_message?.created_at || chat.created_at).toLocaleDateString()}
                                      </span>
                                    </div>
                                    <p className={`text-sm truncate ${
                                      (chat.unread_count || 0) > 0 ? "font-medium" : "text-muted-foreground"
                                    }`}>
                                      {chat.last_message?.message || 'No messages yet'}
                                    </p>
                                    <div className="mt-1 flex items-center">
                                      <Badge
                                        variant={otherParticipant.type === "provider" ? "secondary" : "outline"}
                                        className="text-xs"
                                      >
                                        {otherParticipant.type}
                                      </Badge>
                                      {(chat.unread_count || 0) > 0 && (
                                        <Badge className="ml-2 bg-primary" variant="default">
                                          {chat.unread_count}
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        )}
                      </ScrollArea>
                    </div>
                  )}
                </Card>
              )}
              
              {/* Message Thread - Show on desktop or on mobile when viewing a thread */}
              {(!isMobile || showMessageThread) && (
                <Card className="p-4 flex flex-col h-full md:col-span-2 overflow-hidden bg-gradient-to-b from-blue-50 to-white dark:from-blue-950/40 dark:to-gray-800 border-0 shadow-md rounded-xl">
                  {selectedChat ? (
                    <>
                      {isMobile && (
                        <div className="mb-4">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleBackToList}
                            className="flex items-center p-2 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50 rounded-lg transition-colors"
                          >
                            <ArrowLeft className="h-4 w-4 mr-1" />
                            <span>Back to messages</span>
                          </Button>
                        </div>
                      )}

                      {/* Chat Header */}
                      <div className="flex items-center justify-between mb-4 pb-4 border-b">
                        <div className="flex items-center">
                          <Avatar className="h-10 w-10 mr-3">
                            <AvatarImage src={getOtherParticipant(selectedChat).avatar} />
                            <AvatarFallback>
                              {getOtherParticipant(selectedChat).name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <h3 className="font-semibold">{getOtherParticipant(selectedChat).name}</h3>
                            <p className="text-sm text-muted-foreground">
                              {getOtherParticipant(selectedChat).type}
                            </p>
                          </div>
                        </div>
                        <Badge variant="outline">
                          {getOtherParticipant(selectedChat).type}
                        </Badge>
                      </div>

                      <div className="flex-grow overflow-hidden mb-4">
                        <MessageThread
                          chatId={selectedChat.id}
                          messageEndRef={messageEndRef}
                        />
                      </div>

                      <MessageComposer
                        chatId={selectedChat.id}
                        recipientName={getOtherParticipant(selectedChat).name}
                      />
                    </>
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center max-w-md">
                        <div className="h-20 w-20 bg-indigo-50 dark:bg-indigo-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                          <MessageSquare className="h-10 w-10 text-indigo-500" />
                        </div>
                        <h3 className="text-lg font-medium mb-2">Select a chat</h3>
                        <p className="text-muted-foreground">
                          Choose a chat from the list to view messages and respond
                        </p>
                      </div>
                    </div>
                  )}
                </Card>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="oversight" className="mt-6 animate-fade-in">
            <Card className="p-4 bg-gradient-to-b from-blue-50 to-white dark:from-blue-950/40 dark:to-gray-800 border-0 shadow-md rounded-xl">
              <CustomerProviderConversations />
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};
